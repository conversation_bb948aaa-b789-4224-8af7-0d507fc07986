<!-- Dashboard Layout -->
<div class="dashboard-container">
  <!-- Welcome State -->
  <div *ngIf="tabService.tabsValue.length === 0" class="welcome-state">
    <mat-card class="welcome-card">
      <mat-card-header>
        <div mat-card-avatar class="welcome-avatar">
          <mat-icon>dashboard</mat-icon>
        </div>
        <mat-card-title>Welcome to NS Drive Dashboard</mat-card-title>
        <mat-card-subtitle>
          Start by creating your first sync operation
        </mat-card-subtitle>
      </mat-card-header>

      <mat-card-content>
        <p>
          Sync operations allow you to synchronize files between local
          directories and cloud storage services. Each operation runs
          independently with its own configuration and profile.
        </p>

        <div class="features-list">
          <div class="feature-item">
            <mat-icon color="primary">sync</mat-icon>
            <span>Real-time synchronization</span>
          </div>
          <div class="feature-item">
            <mat-icon color="primary">cloud</mat-icon>
            <span>Multiple cloud providers</span>
          </div>
          <div class="feature-item">
            <mat-icon color="primary">settings</mat-icon>
            <span>Customizable profiles</span>
          </div>
        </div>
      </mat-card-content>

      <mat-card-actions align="end">
        <button mat-raised-button color="primary" (click)="createTab()">
          <mat-icon>add</mat-icon>
          Create First Operation
        </button>
      </mat-card-actions>
    </mat-card>
  </div>

  <!-- Operations Dashboard -->
  <div *ngIf="tabService.tabsValue.length > 0" class="operations-dashboard">
    <!-- Operations Header -->
    <div class="operations-header">
      <div class="header-content">
        <h2 class="header-title">
          <mat-icon class="header-icon">sync</mat-icon>
          Sync Operations
        </h2>
        <p class="header-subtitle">
          Manage and monitor your file synchronization tasks
        </p>
      </div>

      <button
        mat-fab
        color="primary"
        (click)="createTab()"
        matTooltip="Add new operation"
      >
        <mat-icon>add</mat-icon>
      </button>
    </div>

    <!-- Operations Navigation -->
    <mat-card>
      <mat-card-content>
        <div class="operations-tabs">
          <div
            *ngFor="let tab of tabService.tabsValue; let i = index"
            class="operation-tab"
            [class.active]="getActiveTabIndex() === i"
            [class.running]="tab.currentAction"
            (click)="onTabChange(i)"
            (keydown.enter)="onTabChange(i)"
            (keydown.space)="onTabChange(i)"
            tabindex="0"
            role="button"
            [attr.aria-label]="
              'Switch to ' + (tab.name || 'Operation ' + (i + 1))
            "
          >
            <div *ngIf="!tab.isEditing" class="tab-content">
              <mat-icon
                class="tab-icon"
                [class.active-icon]="getActiveTabIndex() === i"
              >
                {{
                  tab.currentAction
                    ? getActionIcon(tab.currentAction)
                    : "folder"
                }}
              </mat-icon>

              <span class="tab-name">
                {{ tab.name || "Operation " + (i + 1) }}
              </span>

              <mat-icon *ngIf="tab.currentAction" class="status-indicator">
                fiber_manual_record
              </mat-icon>

              <button
                mat-icon-button
                [matMenuTriggerFor]="tabMenu"
                (click)="$event.stopPropagation()"
                class="tab-menu-button"
              >
                <mat-icon>more_vert</mat-icon>
              </button>

              <mat-menu #tabMenu="matMenu">
                <button mat-menu-item (click)="startRenameTab(tab.id)">
                  <mat-icon>edit</mat-icon>
                  <span>Rename</span>
                </button>
                <button mat-menu-item (click)="deleteTab(tab.id)">
                  <mat-icon>delete</mat-icon>
                  <span>Delete</span>
                </button>
              </mat-menu>
            </div>

            <!-- Edit Mode -->
            <div *ngIf="tab.isEditing" class="tab-edit-mode">
              <mat-form-field appearance="outline" class="full-width">
                <input
                  matInput
                  #tabNameInput
                  [value]="tab.name"
                  (blur)="finishRenameTab(tab.id, tabNameInput.value)"
                  (keydown.enter)="finishRenameTab(tab.id, tabNameInput.value)"
                  (keydown.escape)="cancelRenameTab(tab.id)"
                  (click)="$event.stopPropagation()"
                  maxlength="20"
                  placeholder="Operation name"
                />
              </mat-form-field>
            </div>
          </div>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- Operations Content -->
    <div class="operations-content">
      <div
        *ngFor="let tab of tabService.tabsValue; let i = index"
        class="operation-content"
        [class.active]="getActiveTabIndex() === i"
      >
        <!-- Control Panel -->
        <mat-card class="control-panel">
          <mat-card-header>
            <mat-card-title class="control-title">
              <mat-icon>settings</mat-icon>
              Operation Controls
            </mat-card-title>
            <mat-card-subtitle>
              Configure and execute sync operations
            </mat-card-subtitle>
          </mat-card-header>

          <mat-card-content>
            <!-- Profile Selection -->
            <mat-form-field appearance="outline" class="profile-select">
              <mat-label>Sync Profile</mat-label>
              <mat-select
                [value]="tab.selectedProfileIndex"
                (selectionChange)="changeProfileTab($event.value, tab.id)"
              >
                <mat-option [value]="null">
                  <em>No profile selected</em>
                </mat-option>
                <mat-option
                  *ngFor="
                    let profile of appService.configInfo$.value.profiles;
                    let idx = index
                  "
                  [value]="idx"
                >
                  {{ profile.name }}
                </mat-option>
              </mat-select>
              <mat-icon matSuffix>folder_shared</mat-icon>
            </mat-form-field>

            <!-- Action Buttons -->
            <div *ngIf="validateTabProfileIndex(tab)" class="action-buttons">
              <button
                mat-raised-button
                [color]="tab.currentAction === Action.Pull ? 'warn' : 'primary'"
                [disabled]="
                  (!validateTabProfileIndex(tab) &&
                    tab.currentAction !== Action.Pull) ||
                  tab.isStopping
                "
                (click)="
                  tab.currentAction !== Action.Pull
                    ? pullTab(tab.id)
                    : stopCommandTab(tab.id)
                "
                class="action-button"
              >
                <mat-icon class="action-icon">{{
                  tab.isStopping
                    ? "hourglass_empty"
                    : tab.currentAction === Action.Pull
                    ? "stop"
                    : "download"
                }}</mat-icon>
                {{
                  tab.isStopping
                    ? "Stopping..."
                    : tab.currentAction === Action.Pull
                    ? "Stop Pull"
                    : "Pull"
                }}
              </button>

              <button
                mat-raised-button
                [color]="tab.currentAction === Action.Push ? 'warn' : 'accent'"
                [disabled]="
                  !validateTabProfileIndex(tab) &&
                  tab.currentAction !== Action.Push
                "
                (click)="
                  tab.currentAction !== Action.Push
                    ? pushTab(tab.id)
                    : stopCommandTab(tab.id)
                "
                class="action-button"
              >
                <mat-icon class="action-icon">{{
                  tab.currentAction === Action.Push ? "stop" : "upload"
                }}</mat-icon>
                {{ tab.currentAction === Action.Push ? "Stop Push" : "Push" }}
              </button>

              <button
                mat-raised-button
                [color]="tab.currentAction === Action.Bi ? 'warn' : 'primary'"
                [disabled]="
                  !validateTabProfileIndex(tab) &&
                  tab.currentAction !== Action.Bi
                "
                (click)="
                  tab.currentAction !== Action.Bi
                    ? biTab(tab.id)
                    : stopCommandTab(tab.id)
                "
                style="height: 56px; font-size: 16px; font-weight: 500"
              >
                <mat-icon style="margin-right: 8px">{{
                  tab.currentAction === Action.Bi ? "stop" : "sync"
                }}</mat-icon>
                {{ tab.currentAction === Action.Bi ? "Stop Sync" : "Sync" }}
              </button>

              <button
                mat-raised-button
                color="warn"
                [disabled]="
                  !validateTabProfileIndex(tab) &&
                  tab.currentAction !== Action.BiResync
                "
                (click)="
                  tab.currentAction !== Action.BiResync
                    ? biResyncTab(tab.id)
                    : stopCommandTab(tab.id)
                "
                style="height: 56px; font-size: 16px; font-weight: 500"
              >
                <mat-icon style="margin-right: 8px">{{
                  tab.currentAction === Action.BiResync ? "stop" : "refresh"
                }}</mat-icon>
                {{
                  tab.currentAction === Action.BiResync
                    ? "Stop Resync"
                    : "Resync"
                }}
              </button>
            </div>

            <!-- Status Display -->
            <div *ngIf="tab.currentAction" style="margin-top: 24px">
              <mat-progress-bar
                mode="indeterminate"
                style="margin-bottom: 16px"
              ></mat-progress-bar>
              <div
                style="
                  display: flex;
                  align-items: center;
                  gap: 8px;
                  padding: 8px 16px;
                  background-color: rgba(76, 175, 80, 0.1);
                  border-radius: 20px;
                  color: #4caf50;
                  font-weight: 500;
                  width: fit-content;
                "
              >
                <mat-icon style="font-size: 18px; width: 18px; height: 18px">{{
                  getActionIcon(tab.currentAction)
                }}</mat-icon>
                <span>{{ getActionLabel(tab.currentAction) }}</span>
              </div>
            </div>
          </mat-card-content>
        </mat-card>

        <!-- Info Cards Grid -->
        <div style="display: grid; grid-template-columns: 1fr 2fr; gap: 24px">
          <!-- Working Directory -->
          <mat-card>
            <mat-card-header>
              <mat-card-title>
                <mat-icon>folder</mat-icon>
                Working Directory
              </mat-card-title>
            </mat-card-header>
            <mat-card-content>
              <div
                style="
                  font-family: 'Courier New', monospace;
                  background-color: rgba(0, 0, 0, 0.05);
                  padding: 16px;
                  border-radius: 8px;
                  word-break: break-all;
                  font-size: 14px;
                "
              >
                {{ (appService.configInfo$ | async)?.working_dir }}
              </div>
            </mat-card-content>
          </mat-card>

          <!-- Console Output -->
          <mat-card>
            <mat-card-header>
              <mat-card-title>
                <mat-icon>terminal</mat-icon>
                Console Output
              </mat-card-title>
              <div style="flex: 1"></div>
              <button
                mat-icon-button
                (click)="clearTabOutput(tab.id)"
                matTooltip="Clear output"
              >
                <mat-icon>clear</mat-icon>
              </button>
            </mat-card-header>
            <mat-card-content>
              <div
                style="
                  background-color: #1e1e1e;
                  color: #d4d4d4;
                  padding: 16px;
                  border-radius: 8px;
                  font-family: 'Courier New', monospace;
                  font-size: 13px;
                  line-height: 1.4;
                  max-height: 400px;
                  overflow: auto;
                "
              >
                <pre
                  style="
                    margin: 0;
                    white-space: pre-wrap;
                    word-wrap: break-word;
                  "
                  >{{ tab.data.join("\n") || "No output yet..." }}</pre
                >
              </div>
            </mat-card-content>
          </mat-card>
        </div>
      </div>
    </div>
  </div>
</div>
